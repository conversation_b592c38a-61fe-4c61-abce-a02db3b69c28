"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Send, Save, Users, User, Globe, Calendar, Link as LinkI<PERSON> } from "lucide-react"
import { NotificationService, type NotificationData } from "@/lib/notification-service"
import { collection, getDocs, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { useAuth } from "@/lib/hooks/use-auth"
import { toast } from "sonner"

const notificationSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(100, "Le titre ne peut pas dépasser 100 caractères"),
  body: z.string().min(1, "Le message est requis").max(500, "Le message ne peut pas dépasser 500 caractères"),
  type: z.enum(["general", "news", "page", "system"]),
  targetType: z.enum(["all", "groups", "users", "individual"]),
  targetGroups: z.array(z.string()).optional(),
  targetUsers: z.array(z.string()).optional(),
  linkType: z.enum(["none", "news", "page"]).optional(),
  linkId: z.string().optional(),
  linkUrl: z.string().url().optional().or(z.literal("")),
  priority: z.enum(["low", "normal", "high"]),
  scheduledAt: z.string().optional(),
  expiresAt: z.string().optional()
})

type NotificationFormData = z.infer<typeof notificationSchema>

interface NotificationFormProps {
  onSuccess?: () => void
  onCancel?: () => void
  initialData?: Partial<NotificationData>
  mode?: "create" | "edit"
}

export function NotificationForm({ 
  onSuccess, 
  onCancel, 
  initialData, 
  mode = "create" 
}: NotificationFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [groups, setGroups] = useState<Array<{ id: string; name: string }>>([])
  const [users, setUsers] = useState<Array<{ id: string; email: string; displayName?: string }>>([])
  const [newsItems, setNewsItems] = useState<Array<{ id: string; title: string }>>([])
  const [pages, setPages] = useState<Array<{ id: string; title: string }>>([])

  const form = useForm<NotificationFormData>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      title: initialData?.title || "",
      body: initialData?.body || "",
      type: initialData?.type || "general",
      targetType: initialData?.targetType || "all",
      targetGroups: initialData?.targetGroups || [],
      targetUsers: initialData?.targetUsers || [],
      linkType: initialData?.linkType || "none",
      linkId: initialData?.linkId || "",
      linkUrl: initialData?.linkUrl || "",
      priority: initialData?.priority || "normal",
      scheduledAt: "",
      expiresAt: ""
    }
  })

  const watchTargetType = form.watch("targetType")
  const watchLinkType = form.watch("linkType")

  // Charger les données de référence
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        // Charger les groupes
        const groupsSnapshot = await getDocs(collection(db(), "groups"))
        const groupsList = groupsSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name
        }))
        setGroups(groupsList)

        // Charger les utilisateurs
        const usersSnapshot = await getDocs(collection(db(), "users"))
        const usersList = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          email: doc.data().email,
          displayName: doc.data().displayName
        }))
        setUsers(usersList)

        // Charger les actualités publiées
        const newsQuery = query(
          collection(db(), "news"),
          where("isPublished", "==", true)
        )
        const newsSnapshot = await getDocs(newsQuery)
        const newsList = newsSnapshot.docs.map(doc => ({
          id: doc.id,
          title: doc.data().title
        }))
        setNewsItems(newsList)

        // Charger les pages publiées
        const pagesQuery = query(
          collection(db(), "menuItems"),
          where("isPublished", "==", true)
        )
        const pagesSnapshot = await getDocs(pagesQuery)
        const pagesList = pagesSnapshot.docs.map(doc => ({
          id: doc.id,
          title: doc.data().title
        }))
        setPages(pagesList)
      } catch (error) {
        console.error("Erreur lors du chargement des données de référence:", error)
        toast.error("Impossible de charger les données de référence")
      }
    }

    loadReferenceData()
  }, [])

  const onSubmit = async (data: NotificationFormData) => {
    if (!user?.uid) return

    try {
      setLoading(true)

      const notificationData: Omit<NotificationData, 'id' | 'createdAt' | 'updatedAt'> = {
        title: data.title,
        body: data.body,
        type: data.type,
        targetType: data.targetType,
        targetGroups: data.targetType === "groups" ? data.targetGroups : undefined,
        targetUsers: data.targetType === "users" || data.targetType === "individual" ? data.targetUsers : undefined,
        linkType: data.linkType,
        linkId: data.linkType !== "none" ? data.linkId : undefined,
        linkUrl: data.linkUrl || undefined,
        priority: data.priority,
        scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : undefined,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
        createdBy: user.uid,
        status: data.scheduledAt ? "scheduled" : "draft"
      }

      if (mode === "create") {
        await NotificationService.createNotification(notificationData)
        toast.success("Notification créée avec succès")
      } else {
        // Mode édition - à implémenter
        toast.success("Notification mise à jour avec succès")
      }

      onSuccess?.()
    } catch (error) {
      console.error("Erreur lors de la sauvegarde:", error)
      toast.error("Impossible de sauvegarder la notification")
    } finally {
      setLoading(false)
    }
  }

  const handleSendNow = async () => {
    const data = form.getValues()
    // Valider d'abord
    const isValid = await form.trigger()
    if (!isValid) return

    // Envoyer immédiatement
    await onSubmit({ ...data, scheduledAt: "" })
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Send className="h-5 w-5" />
          {mode === "create" ? "Créer une notification" : "Modifier la notification"}
        </CardTitle>
        <CardDescription>
          Envoyez des notifications push et in-app à vos utilisateurs
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Contenu de la notification */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Contenu</h3>
              
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Titre</FormLabel>
                    <FormControl>
                      <Input placeholder="Titre de la notification" {...field} />
                    </FormControl>
                    <FormDescription>
                      Le titre apparaîtra en gras dans la notification
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="body"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Contenu de la notification"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Le message principal de la notification
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="general">Général</SelectItem>
                          <SelectItem value="news">Actualité</SelectItem>
                          <SelectItem value="page">Page</SelectItem>
                          <SelectItem value="system">Système</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priorité</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une priorité" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Basse</SelectItem>
                          <SelectItem value="normal">Normale</SelectItem>
                          <SelectItem value="high">Haute</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Ciblage */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Users className="h-5 w-5" />
                Ciblage
              </h3>

              <FormField
                control={form.control}
                name="targetType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Destinataires</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner les destinataires" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4" />
                            Tous les utilisateurs
                          </div>
                        </SelectItem>
                        <SelectItem value="groups">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            Groupes spécifiques
                          </div>
                        </SelectItem>
                        <SelectItem value="users">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            Utilisateurs spécifiques
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {watchTargetType === "groups" && (
                <FormField
                  control={form.control}
                  name="targetGroups"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Groupes cibles</FormLabel>
                      <div className="space-y-2">
                        {groups.map((group) => (
                          <div key={group.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={group.id}
                              checked={field.value?.includes(group.id)}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || []
                                if (checked) {
                                  field.onChange([...currentValue, group.id])
                                } else {
                                  field.onChange(currentValue.filter(id => id !== group.id))
                                }
                              }}
                            />
                            <label htmlFor={group.id} className="text-sm font-medium">
                              {group.name}
                            </label>
                          </div>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {(watchTargetType === "users" || watchTargetType === "individual") && (
                <FormField
                  control={form.control}
                  name="targetUsers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Utilisateurs cibles</FormLabel>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {users.map((user) => (
                          <div key={user.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={user.id}
                              checked={field.value?.includes(user.id)}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || []
                                if (checked) {
                                  field.onChange([...currentValue, user.id])
                                } else {
                                  field.onChange(currentValue.filter(id => id !== user.id))
                                }
                              }}
                            />
                            <label htmlFor={user.id} className="text-sm font-medium">
                              {user.displayName || user.email}
                            </label>
                          </div>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <Separator />

            {/* Actions */}
            <div className="flex justify-end gap-2">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Annuler
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Save className="mr-2 h-4 w-4" />
                Sauvegarder
              </Button>
              <Button type="button" onClick={handleSendNow} disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Send className="mr-2 h-4 w-4" />
                Envoyer maintenant
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
