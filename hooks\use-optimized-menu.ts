"use client"

import { useState, useEffect, useCallback } from "react"
import { useAuth } from "@/components/auth-provider"
import { useGroups } from "@/components/group-context"
import { optimizedMenuService } from "@/lib/optimized-menu-service"

interface MenuItem {
  id: string
  title: string
  path: string
  iconUrl: string | null
  displayOrder: number
}

interface UseOptimizedMenuReturn {
  menuItems: MenuItem[]
  isMenuLoading: boolean
  menuError: string | null
  refreshMenu: () => Promise<void>
  preloadMenu: () => Promise<void>
}

export function useOptimizedMenu(): UseOptimizedMenuReturn {
  const { user } = useAuth()
  const { userGroups, isGroupsLoading } = useGroups()
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [isMenuLoading, setIsMenuLoading] = useState(true)
  const [menuError, setMenuError] = useState<string | null>(null)

  // Load menu items
  const loadMenuItems = useCallback(async (forceRefresh = false) => {
    if (!user?.uid || isGroupsLoading) {
      return
    }

    try {
      setIsMenuLoading(true)
      setMenuError(null)

      // Clear cache if force refresh
      if (forceRefresh) {
        optimizedMenuService.clearCache(user.uid)
      }

      console.log(`Fetching menu items with groups: ${JSON.stringify(userGroups)}`)
      const items = await optimizedMenuService.getMenuItemsForGroups(userGroups, user.uid)
      
      setMenuItems(items)
      console.log(`Menu items loaded: ${items.length} items`)
    } catch (error) {
      console.error("Erreur lors du chargement du menu:", error)
      setMenuError(error instanceof Error ? error.message : "Erreur inconnue")
      
      // Try to use any cached data as fallback
      try {
        const cachedItems = await optimizedMenuService.getMenuItemsForGroups(userGroups, user.uid)
        if (cachedItems.length > 0) {
          setMenuItems(cachedItems)
          console.log("Utilisation du cache comme fallback pour le menu")
        }
      } catch {
        // If all fails, set empty menu
        setMenuItems([])
      }
    } finally {
      setIsMenuLoading(false)
    }
  }, [user?.uid, userGroups, isGroupsLoading])

  // Refresh menu function
  const refreshMenu = useCallback(async () => {
    await loadMenuItems(true)
  }, [loadMenuItems])

  // Preload menu function
  const preloadMenu = useCallback(async () => {
    if (user?.uid && userGroups.length > 0) {
      await optimizedMenuService.preloadMenuItems(userGroups, user.uid)
    }
  }, [user?.uid, userGroups])

  // Load menu when user or groups change
  useEffect(() => {
    if (user?.uid && !isGroupsLoading && userGroups.length > 0) {
      loadMenuItems()
    } else if (!user?.uid) {
      // Clear menu when user logs out
      setMenuItems([])
      setIsMenuLoading(false)
      setMenuError(null)
    }
  }, [user?.uid, userGroups, isGroupsLoading, loadMenuItems])

  // Preload menu items when groups are available
  useEffect(() => {
    if (user?.uid && userGroups.length > 0 && !isGroupsLoading) {
      // Preload in background after a short delay
      setTimeout(() => {
        preloadMenu()
      }, 100)
    }
  }, [user?.uid, userGroups, isGroupsLoading, preloadMenu])

  return {
    menuItems,
    isMenuLoading,
    menuError,
    refreshMenu,
    preloadMenu,
  }
}
