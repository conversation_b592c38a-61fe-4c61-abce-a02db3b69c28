"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, useC<PERSON>back, type ReactNode } from "react"
import { useAuth } from "@/components/auth-provider"
import { getUserGroups } from "@/lib/user-utils"

interface GroupContextType {
  userGroups: string[]
  isGroupsLoading: boolean
  groupsError: string | null
  refreshGroups: () => Promise<void>
  hasGroup: (group: string) => boolean
  hasAnyGroup: (groups: string[]) => boolean
}

const GroupContext = createContext<GroupContextType | undefined>(undefined)

export function useGroups(): GroupContextType {
  const context = useContext(GroupContext)
  if (!context) {
    throw new Error("useGroups must be used within a GroupProvider")
  }
  return context
}

interface GroupProviderProps {
  children: ReactNode
}

export function GroupProvider({ children }: GroupProviderProps) {
  const { user } = useAuth()
  const [userGroups, setUserGroups] = useState<string[]>([])
  const [isGroupsLoading, setIsGroupsLoading] = useState(true)
  const [groupsError, setGroupsError] = useState<string | null>(null)

  // Optimized group fetching with aggressive caching
  const fetchGroups = useCallback(async (userId: string, useCache = true) => {
    try {
      setIsGroupsLoading(true)
      setGroupsError(null)

      // Check cache first if enabled
      if (useCache) {
        const cachedGroups = localStorage.getItem(`user_groups_${userId}`)
        const cachedTimestamp = localStorage.getItem(`user_groups_timestamp_${userId}`)
        
        if (cachedGroups && cachedTimestamp) {
          const cacheAge = Date.now() - parseInt(cachedTimestamp)
          // Use cache if less than 5 minutes old
          if (cacheAge < 5 * 60 * 1000) {
            const groups = JSON.parse(cachedGroups)
            setUserGroups(groups)
            setIsGroupsLoading(false)
            console.log(`Utilisation des groupes en cache pour l'utilisateur: ${userId}`, groups)
            return groups
          }
        }
      }

      // Fetch from Firestore
      console.log(`Récupération des groupes pour l'utilisateur: ${userId}`)
      const groups = await getUserGroups(userId)
      
      // Update state
      setUserGroups(groups)
      
      // Cache the results
      localStorage.setItem(`user_groups_${userId}`, JSON.stringify(groups))
      localStorage.setItem(`user_groups_timestamp_${userId}`, Date.now().toString())
      
      console.log(`Groupes utilisateur mis en cache: ${JSON.stringify(groups)}`)
      return groups
    } catch (error) {
      console.error("Erreur lors de la récupération des groupes:", error)
      setGroupsError(error instanceof Error ? error.message : "Erreur inconnue")
      
      // Try to use cached data as fallback
      const cachedGroups = localStorage.getItem(`user_groups_${userId}`)
      if (cachedGroups) {
        const groups = JSON.parse(cachedGroups)
        setUserGroups(groups)
        console.log("Utilisation des groupes en cache comme fallback:", groups)
        return groups
      }
      
      return []
    } finally {
      setIsGroupsLoading(false)
    }
  }, [])

  // Refresh groups function
  const refreshGroups = useCallback(async () => {
    if (user?.uid) {
      await fetchGroups(user.uid, false) // Force refresh without cache
    }
  }, [user?.uid, fetchGroups])

  // Helper functions
  const hasGroup = useCallback((group: string) => {
    return userGroups.includes(group) || userGroups.includes("all")
  }, [userGroups])

  const hasAnyGroup = useCallback((groups: string[]) => {
    return groups.some(group => hasGroup(group))
  }, [hasGroup])

  // Load groups when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchGroups(user.uid)
    } else {
      setUserGroups([])
      setIsGroupsLoading(false)
      setGroupsError(null)
    }
  }, [user?.uid, fetchGroups])

  const contextValue: GroupContextType = {
    userGroups,
    isGroupsLoading,
    groupsError,
    refreshGroups,
    hasGroup,
    hasAnyGroup,
  }

  return (
    <GroupContext.Provider value={contextValue}>
      {children}
    </GroupContext.Provider>
  )
}
