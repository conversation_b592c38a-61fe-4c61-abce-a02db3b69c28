import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  serverTimestamp,
  onSnapshot,
  type Unsubscribe
} from "firebase/firestore"
import { db, messaging } from "@/lib/firebase"
import { getToken, onMessage, deleteToken } from "firebase/messaging"

// Types pour les notifications
export interface NotificationData {
  id?: string
  title: string
  body: string
  type: 'general' | 'news' | 'page' | 'system'
  targetType: 'all' | 'groups' | 'users' | 'individual'
  targetGroups?: string[]
  targetUsers?: string[]
  linkType?: 'none' | 'news' | 'page'
  linkId?: string
  linkUrl?: string
  priority: 'low' | 'normal' | 'high'
  scheduledAt?: Date
  expiresAt?: Date
  createdBy: string
  createdAt?: any
  updatedAt?: any
  status: 'draft' | 'scheduled' | 'sent' | 'failed'
  sentCount?: number
  readCount?: number
  clickCount?: number
  metadata?: Record<string, any>
}

export interface UserNotification {
  id?: string
  notificationId: string
  userId: string
  isRead: boolean
  isClicked: boolean
  readAt?: Date
  clickedAt?: Date
  createdAt?: any
}

export interface NotificationPreferences {
  userId: string
  pushEnabled: boolean
  inAppEnabled: boolean
  emailEnabled?: boolean
  categories: {
    general: boolean
    news: boolean
    pages: boolean
    system: boolean
  }
  quietHours?: {
    enabled: boolean
    start: string // HH:mm format
    end: string // HH:mm format
  }
  updatedAt?: any
}

/**
 * Service pour gérer les notifications
 */
export const NotificationService = {
  /**
   * Initialise le service de notifications pour un utilisateur
   */
  async initializeForUser(userId: string): Promise<string | null> {
    try {
      // Vérifier si FCM est supporté
      const messagingInstance = messaging()
      if (!messagingInstance) {
        console.warn("Firebase Cloud Messaging non disponible")
        return null
      }

      // Demander la permission pour les notifications
      const permission = await Notification.requestPermission()
      if (permission !== 'granted') {
        console.warn("Permission de notification refusée")
        return null
      }

      // Obtenir le token FCM
      const token = await getToken(messagingInstance, {
        vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY
      })

      if (token) {
        // Sauvegarder le token dans Firestore
        await this.saveUserToken(userId, token)
        console.log("Token FCM sauvegardé:", token)
        return token
      }

      return null
    } catch (error) {
      console.error("Erreur lors de l'initialisation des notifications:", error)
      return null
    }
  },

  /**
   * Sauvegarde le token FCM d'un utilisateur
   */
  async saveUserToken(userId: string, token: string): Promise<void> {
    try {
      const userTokenRef = doc(db(), "userTokens", userId)
      await updateDoc(userTokenRef, {
        fcmToken: token,
        updatedAt: serverTimestamp(),
        platform: this.getPlatform(),
        userAgent: navigator.userAgent
      }).catch(async () => {
        // Si le document n'existe pas, le créer
        await addDoc(collection(db(), "userTokens"), {
          userId,
          fcmToken: token,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          platform: this.getPlatform(),
          userAgent: navigator.userAgent
        })
      })
    } catch (error) {
      console.error("Erreur lors de la sauvegarde du token:", error)
      throw error
    }
  },

  /**
   * Supprime le token FCM d'un utilisateur
   */
  async removeUserToken(userId: string): Promise<void> {
    try {
      const messagingInstance = messaging()
      if (messagingInstance) {
        await deleteToken(messagingInstance)
      }

      const userTokenRef = doc(db(), "userTokens", userId)
      await deleteDoc(userTokenRef)
    } catch (error) {
      console.error("Erreur lors de la suppression du token:", error)
      throw error
    }
  },

  /**
   * Écoute les messages en temps réel
   */
  onMessage(callback: (payload: any) => void): Unsubscribe | null {
    try {
      const messagingInstance = messaging()
      if (!messagingInstance) return null

      return onMessage(messagingInstance, callback)
    } catch (error) {
      console.error("Erreur lors de l'écoute des messages:", error)
      return null
    }
  },

  /**
   * Crée une nouvelle notification
   */
  async createNotification(notificationData: Omit<NotificationData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db(), "notifications"), {
        ...notificationData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        sentCount: 0,
        readCount: 0,
        clickCount: 0
      })

      return docRef.id
    } catch (error) {
      console.error("Erreur lors de la création de la notification:", error)
      throw error
    }
  },

  /**
   * Met à jour une notification
   */
  async updateNotification(notificationId: string, updates: Partial<NotificationData>): Promise<void> {
    try {
      const notificationRef = doc(db(), "notifications", notificationId)
      await updateDoc(notificationRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la notification:", error)
      throw error
    }
  },

  /**
   * Supprime une notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(db(), "notifications", notificationId)
      await deleteDoc(notificationRef)

      // Supprimer aussi toutes les notifications utilisateur associées
      const userNotificationsQuery = query(
        collection(db(), "userNotifications"),
        where("notificationId", "==", notificationId)
      )
      const userNotificationsSnapshot = await getDocs(userNotificationsQuery)
      
      const deletePromises = userNotificationsSnapshot.docs.map(doc => deleteDoc(doc.ref))
      await Promise.all(deletePromises)
    } catch (error) {
      console.error("Erreur lors de la suppression de la notification:", error)
      throw error
    }
  },

  /**
   * Récupère les notifications d'un utilisateur
   */
  async getUserNotifications(userId: string, limitCount: number = 50): Promise<UserNotification[]> {
    try {
      const userNotificationsQuery = query(
        collection(db(), "userNotifications"),
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      )

      const snapshot = await getDocs(userNotificationsQuery)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as UserNotification))
    } catch (error) {
      console.error("Erreur lors de la récupération des notifications utilisateur:", error)
      throw error
    }
  },

  /**
   * Marque une notification comme lue
   */
  async markAsRead(userNotificationId: string): Promise<void> {
    try {
      const userNotificationRef = doc(db(), "userNotifications", userNotificationId)
      await updateDoc(userNotificationRef, {
        isRead: true,
        readAt: serverTimestamp()
      })
    } catch (error) {
      console.error("Erreur lors du marquage comme lu:", error)
      throw error
    }
  },

  /**
   * Marque une notification comme cliquée
   */
  async markAsClicked(userNotificationId: string): Promise<void> {
    try {
      const userNotificationRef = doc(db(), "userNotifications", userNotificationId)
      await updateDoc(userNotificationRef, {
        isClicked: true,
        clickedAt: serverTimestamp()
      })
    } catch (error) {
      console.error("Erreur lors du marquage comme cliqué:", error)
      throw error
    }
  },

  /**
   * Récupère les préférences de notification d'un utilisateur
   */
  async getUserPreferences(userId: string): Promise<NotificationPreferences | null> {
    try {
      const preferencesRef = doc(db(), "notificationPreferences", userId)
      const snapshot = await getDoc(preferencesRef)
      
      if (snapshot.exists()) {
        return snapshot.data() as NotificationPreferences
      }

      // Créer des préférences par défaut si elles n'existent pas
      const defaultPreferences: NotificationPreferences = {
        userId,
        pushEnabled: true,
        inAppEnabled: true,
        emailEnabled: false,
        categories: {
          general: true,
          news: true,
          pages: true,
          system: true
        },
        quietHours: {
          enabled: false,
          start: "22:00",
          end: "08:00"
        }
      }

      await this.updateUserPreferences(userId, defaultPreferences)
      return defaultPreferences
    } catch (error) {
      console.error("Erreur lors de la récupération des préférences:", error)
      return null
    }
  },

  /**
   * Met à jour les préférences de notification d'un utilisateur
   */
  async updateUserPreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      const preferencesRef = doc(db(), "notificationPreferences", userId)
      await updateDoc(preferencesRef, {
        ...preferences,
        updatedAt: serverTimestamp()
      }).catch(async () => {
        // Si le document n'existe pas, le créer
        await addDoc(collection(db(), "notificationPreferences"), {
          userId,
          ...preferences,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des préférences:", error)
      throw error
    }
  },

  /**
   * Détermine la plateforme actuelle
   */
  getPlatform(): string {
    if (typeof window === 'undefined') return 'server'
    
    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.includes('android')) return 'android'
    if (userAgent.includes('iphone') || userAgent.includes('ipad')) return 'ios'
    if (userAgent.includes('windows')) return 'windows'
    if (userAgent.includes('mac')) return 'macos'
    if (userAgent.includes('linux')) return 'linux'
    
    return 'web'
  }
}
