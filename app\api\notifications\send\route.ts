import { NextRequest, NextResponse } from "next/server"
import { initializeFirebaseAdmin } from "@/lib/server-auth"
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  query, 
  where, 
  serverTimestamp 
} from "firebase/firestore"

interface SendNotificationRequest {
  title: string
  body: string
  type: 'general' | 'news' | 'page' | 'system'
  targetType: 'all' | 'groups' | 'users' | 'individual'
  targetGroups?: string[]
  targetUsers?: string[]
  linkType?: 'none' | 'news' | 'page'
  linkId?: string
  linkUrl?: string
  priority: 'low' | 'normal' | 'high'
  scheduledAt?: string
  expiresAt?: string
}

export async function POST(request: NextRequest) {
  try {
    // Vérifier l'authentification
    const sessionCookie = request.cookies.get("__session")?.value
    if (!sessionCookie) {
      return NextResponse.json(
        { error: "Non authentifié" },
        { status: 401 }
      )
    }

    // Initialiser Firebase Admin
    const { auth, firestore } = initializeFirebaseAdmin()
    
    // Vérifier le token de session
    const decodedToken = await auth.verifySessionCookie(sessionCookie, true)
    
    // Vérifier si l'utilisateur est admin
    const adminDoc = await firestore.collection("admins").doc(decodedToken.email!).get()
    if (!adminDoc.exists) {
      return NextResponse.json(
        { error: "Accès non autorisé" },
        { status: 403 }
      )
    }

    // Parser les données de la requête
    const data: SendNotificationRequest = await request.json()

    // Valider les données requises
    if (!data.title || !data.body || !data.type || !data.targetType || !data.priority) {
      return NextResponse.json(
        { error: "Données manquantes" },
        { status: 400 }
      )
    }

    // Créer la notification dans Firestore
    const notificationData = {
      title: data.title,
      body: data.body,
      type: data.type,
      targetType: data.targetType,
      targetGroups: data.targetGroups || [],
      targetUsers: data.targetUsers || [],
      linkType: data.linkType || 'none',
      linkId: data.linkId || null,
      linkUrl: data.linkUrl || null,
      priority: data.priority,
      scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : null,
      expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
      createdBy: decodedToken.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      status: data.scheduledAt ? 'scheduled' : 'sent',
      sentCount: 0,
      readCount: 0,
      clickCount: 0
    }

    const notificationRef = await firestore.collection("notifications").add(notificationData)
    const notificationId = notificationRef.id

    // Si la notification doit être envoyée immédiatement
    if (!data.scheduledAt) {
      // Récupérer les utilisateurs cibles
      let targetUserIds: string[] = []

      if (data.targetType === 'all') {
        // Tous les utilisateurs
        const usersSnapshot = await firestore.collection("users").get()
        targetUserIds = usersSnapshot.docs.map(doc => doc.id)
      } else if (data.targetType === 'groups' && data.targetGroups?.length) {
        // Utilisateurs des groupes spécifiés
        const usersSnapshot = await firestore.collection("users")
          .where("groups", "array-contains-any", data.targetGroups)
          .get()
        targetUserIds = usersSnapshot.docs.map(doc => doc.id)
      } else if (data.targetType === 'users' && data.targetUsers?.length) {
        // Utilisateurs spécifiés
        targetUserIds = data.targetUsers
      }

      // Créer les notifications utilisateur
      const userNotificationPromises = targetUserIds.map(userId => 
        firestore.collection("userNotifications").add({
          notificationId,
          userId,
          isRead: false,
          isClicked: false,
          createdAt: serverTimestamp()
        })
      )

      await Promise.all(userNotificationPromises)

      // Récupérer les tokens FCM des utilisateurs cibles
      const tokensSnapshot = await firestore.collection("userTokens")
        .where("userId", "in", targetUserIds.slice(0, 10)) // Firestore limite à 10 éléments dans 'in'
        .get()

      const tokens = tokensSnapshot.docs
        .map(doc => doc.data().fcmToken)
        .filter(token => token)

      // Envoyer les notifications push si des tokens sont disponibles
      if (tokens.length > 0) {
        try {
          const messaging = (await import('firebase-admin/messaging')).getMessaging()
          
          // Construire l'URL de destination
          let clickAction = "/dashboard/notifications"
          if (data.linkType === 'news' && data.linkId) {
            clickAction = `/dashboard/news/${data.linkId}`
          } else if (data.linkType === 'page' && data.linkId) {
            clickAction = `/dashboard/pages/${data.linkId}`
          } else if (data.linkUrl) {
            clickAction = data.linkUrl
          }

          const message = {
            notification: {
              title: data.title,
              body: data.body,
            },
            data: {
              notificationId,
              type: data.type,
              linkType: data.linkType || 'none',
              linkId: data.linkId || '',
              url: clickAction,
              priority: data.priority
            },
            webpush: {
              notification: {
                icon: "/android-chrome-192x192.png",
                badge: "/favicon-32x32.png",
                tag: "acr-direct-notification",
                requireInteraction: data.priority === "high",
                silent: data.priority === "low",
                actions: [
                  {
                    action: "open",
                    title: "Ouvrir"
                  },
                  {
                    action: "dismiss",
                    title: "Ignorer"
                  }
                ]
              },
              fcmOptions: {
                link: clickAction
              }
            },
            tokens: tokens
          }

          // Ajouter des actions spécifiques selon le type
          if (data.type === "news" && data.linkId) {
            message.webpush.notification.actions.unshift({
              action: "read_news",
              title: "Lire l'article"
            })
          } else if (data.type === "page" && data.linkId) {
            message.webpush.notification.actions.unshift({
              action: "view_page",
              title: "Voir la page"
            })
          }

          const response = await messaging.sendEachForMulticast(message)
          
          console.log(`Notifications envoyées: ${response.successCount}/${tokens.length}`)
          
          // Mettre à jour les statistiques
          await notificationRef.update({
            sentCount: targetUserIds.length,
            pushSentCount: response.successCount,
            pushFailedCount: response.failureCount
          })

        } catch (error) {
          console.error("Erreur lors de l'envoi des notifications push:", error)
          
          // Marquer comme échec partiel
          await notificationRef.update({
            status: 'failed',
            error: error instanceof Error ? error.message : 'Erreur inconnue'
          })
        }
      } else {
        // Pas de tokens FCM, mais les notifications in-app sont créées
        await notificationRef.update({
          sentCount: targetUserIds.length,
          pushSentCount: 0
        })
      }
    }

    return NextResponse.json({
      success: true,
      notificationId,
      message: data.scheduledAt ? "Notification programmée avec succès" : "Notification envoyée avec succès"
    })

  } catch (error) {
    console.error("Erreur lors de l'envoi de la notification:", error)
    
    return NextResponse.json(
      { 
        error: "Erreur interne du serveur",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      },
      { status: 500 }
    )
  }
}
