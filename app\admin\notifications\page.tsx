"use client"

import { useState } from "react"
import { NotificationList } from "@/components/admin/notification-list"
import { NotificationForm } from "@/components/admin/notification-form"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Bell, Settings, BarChart3, Users, Send } from "lucide-react"
import { type NotificationData } from "@/lib/notification-service"
import Link from "next/link"

export default function NotificationsAdminPage() {
  const [activeTab, setActiveTab] = useState("list")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<NotificationData | null>(null)

  const handleCreate = () => {
    setShowCreateDialog(true)
  }

  const handleEdit = (notification: NotificationData) => {
    setSelectedNotification(notification)
    setShowEditDialog(true)
  }

  const handleView = (notification: NotificationData) => {
    setSelectedNotification(notification)
    // Vous pouvez implémenter une vue détaillée ici
    console.log("Voir notification:", notification)
  }

  const handleSuccess = () => {
    setShowCreateDialog(false)
    setShowEditDialog(false)
    setSelectedNotification(null)
  }

  const handleCancel = () => {
    setShowCreateDialog(false)
    setShowEditDialog(false)
    setSelectedNotification(null)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-muted-foreground">
              Gérez les notifications push et in-app de votre application
            </p>
          </div>
        </div>
        <Button onClick={handleCreate}>
          <Send className="h-4 w-4 mr-2" />
          Nouvelle notification
        </Button>
      </div>

      {/* Onglets */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytiques
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Utilisateurs
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Paramètres
          </TabsTrigger>
        </TabsList>

        {/* Liste des notifications */}
        <TabsContent value="list" className="space-y-6">
          <NotificationList
            onCreate={handleCreate}
            onEdit={handleEdit}
            onView={handleView}
          />
        </TabsContent>

        {/* Analytiques */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Notifications envoyées</CardTitle>
                <Send className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <p className="text-xs text-muted-foreground">
                  +20.1% par rapport au mois dernier
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Taux d'ouverture</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">73.2%</div>
                <p className="text-xs text-muted-foreground">
                  +2.5% par rapport au mois dernier
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Taux de clic</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24.8%</div>
                <p className="text-xs text-muted-foreground">
                  +1.2% par rapport au mois dernier
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Utilisateurs actifs</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">892</div>
                <p className="text-xs text-muted-foreground">
                  +12.3% par rapport au mois dernier
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Performances des notifications</CardTitle>
              <CardDescription>
                Analyse détaillée des performances de vos notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Graphiques d'analytiques à implémenter
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Gestion des utilisateurs */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tokens FCM des utilisateurs</CardTitle>
              <CardDescription>
                Gérez les tokens de notification des utilisateurs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Interface de gestion des tokens utilisateurs à implémenter
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Paramètres */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configuration des notifications</CardTitle>
              <CardDescription>
                Paramètres globaux pour le système de notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Notifications push activées</h4>
                    <p className="text-sm text-muted-foreground">
                      Permet l'envoi de notifications push
                    </p>
                  </div>
                  <Badge variant="default">Activé</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Notifications in-app activées</h4>
                    <p className="text-sm text-muted-foreground">
                      Permet l'affichage de notifications dans l'application
                    </p>
                  </div>
                  <Badge variant="default">Activé</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Limite quotidienne</h4>
                    <p className="text-sm text-muted-foreground">
                      Nombre maximum de notifications par utilisateur par jour
                    </p>
                  </div>
                  <Badge variant="outline">10</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium">Heures silencieuses</h4>
                    <p className="text-sm text-muted-foreground">
                      Période pendant laquelle les notifications ne sont pas envoyées
                    </p>
                  </div>
                  <Badge variant="outline">22:00 - 08:00</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialog de création */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Créer une nouvelle notification</DialogTitle>
          </DialogHeader>
          <NotificationForm
            mode="create"
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog d'édition */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Modifier la notification</DialogTitle>
          </DialogHeader>
          <NotificationForm
            mode="edit"
            initialData={selectedNotification || undefined}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
